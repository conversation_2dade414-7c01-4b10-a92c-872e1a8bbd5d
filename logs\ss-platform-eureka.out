[2m2025-07-31 08:52:56.009[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m The following profiles are active: dev
[2m2025-07-31 08:52:56.838[0;39m [dev] [33m WARN[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-31 08:52:57.011[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ff1a2d2c-d19d-3931-a591-81b4348e9e38
[2m2025-07-31 08:52:57.662[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 7602 (http)
[2m2025-07-31 08:52:58.607[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 2572 ms
[2m2025-07-31 08:52:58.778[0;39m [dev] [33m WARN[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-31 08:52:58.778[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-31 08:52:58.801[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.netflix.config.DynamicPropertyFactory [0;39m [2m:[0;39m DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@7d44a19
[2m2025-07-31 08:53:00.217[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-31 08:53:00.218[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-31 08:53:00.432[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-31 08:53:00.432[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-31 08:53:01.148[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@73aaec54, org.springframework.security.web.context.SecurityContextPersistenceFilter@15994b0b, org.springframework.security.web.header.HeaderWriterFilter@6775c0d1, org.springframework.security.web.authentication.logout.LogoutFilter@7d8b66d9, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@1a0f349, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1a717d79, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@10e56da9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@239b98cb, org.springframework.security.web.session.SessionManagementFilter@53bb71e5, org.springframework.security.web.access.ExceptionTranslationFilter@2eebce87, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2b170932]
[2m2025-07-31 08:53:01.160[0;39m [dev] [33m WARN[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-31 08:53:01.160[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-31 08:53:01.324[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService 'applicationTaskExecutor'
[2m2025-07-31 08:53:02.446[0;39m [dev] [33m WARN[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mockingLoadBalancerClientRibbonWarnLogger[0;39m [2m:[0;39m You already have RibbonLoadBalancerClient on your classpath. It will be used by default. As Spring Cloud Ribbon is in maintenance mode. We recommend switching to BlockingLoadBalancerClient instead. In order to use it, set the value of `spring.cloud.loadbalancer.ribbon.enabled` to `false` or remove spring-cloud-starter-netflix-ribbon from your project.
[2m2025-07-31 08:53:02.592[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-31 08:53:02.651[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Initializing Eureka in region us-east-1
[2m2025-07-31 08:53:02.651[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Client configured to neither register nor query for data.
[2m2025-07-31 08:53:02.664[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Discovery Client initialized at timestamp 1753923182663 with initial instances count: 0
[2m2025-07-31 08:53:02.722[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initializing ...
[2m2025-07-31 08:53:02.724[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Adding new peer nodes [**********************************************/eureka/]
[2m2025-07-31 08:53:03.204[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-31 08:53:03.205[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-31 08:53:03.205[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-31 08:53:03.205[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-31 08:53:03.294[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Replica node URL:  **********************************************/eureka/
[2m2025-07-31 08:53:03.304[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Finished initializing remote region registries. All known remote regions: []
[2m2025-07-31 08:53:03.304[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initialized
[2m2025-07-31 08:53:03.317[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 2 endpoint(s) beneath base path '/actuator'
[2m2025-07-31 08:53:03.396[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application SS-PLATFORM-EUREKA with eureka with status UP
[2m2025-07-31 08:53:03.399[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Setting the eureka configuration..
[2m2025-07-31 08:53:03.399[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka data center value eureka.datacenter is not set, defaulting to default
[2m2025-07-31 08:53:03.399[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka environment value eureka.environment is not set, defaulting to test
[2m2025-07-31 08:53:03.415[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m isAws returned false
[2m2025-07-31 08:53:03.416[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Initialized server context
[2m2025-07-31 08:53:03.416[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Got 1 instances from neighboring DS node
[2m2025-07-31 08:53:03.416[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Renew threshold is: 1
[2m2025-07-31 08:53:03.416[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Changing status to UP
[2m2025-07-31 08:53:03.430[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36me.s.EurekaServerInitializerConfiguration[0;39m [2m:[0;39m Started Eureka Server
[2m2025-07-31 08:53:03.460[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 7602 (http) with context path ''
[2m2025-07-31 08:53:03.461[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 7602
[2m2025-07-31 08:53:03.963[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m Started EurekaServerApplication in 10.009 seconds (JVM running for 22.572)
[2m2025-07-31 08:53:04.886[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[2)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-31 08:53:04.920[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[2)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 34 ms
[2m2025-07-31 08:53:20.651[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=false)
[2m2025-07-31 08:53:21.482[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=true)
[2m2025-07-31 08:53:29.780[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=false)
[2m2025-07-31 08:53:29.951[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=false)
[2m2025-07-31 08:53:30.302[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=true)
[2m2025-07-31 08:53:30.303[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=true)
[2m2025-07-31 08:53:34.511[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=false)
[2m2025-07-31 08:53:35.034[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=true)
[2m2025-07-31 08:54:03.426[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-31 08:55:03.442[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-31 08:56:03.454[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-31 08:57:03.463[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-31 08:57:53.533[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-31 08:57:54.065[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-31 08:58:03.487[0;39m [dev] [32m INFO[0;39m [35m54864[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 25ms
